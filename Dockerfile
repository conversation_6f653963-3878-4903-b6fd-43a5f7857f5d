# Use an official Python runtime as the base image
FROM python:3.9-slim

RUN apt update && apt install -y \
    libglib2.0-0 \
    libjpeg62-turbo \
    libopenjp2-7 \
    libmagic1 \
    libreoffice \
    && rm -rf /var/lib/apt/lists/*
    # ttf-mscorefonts-installer \

# Set the working directory in the container
WORKDIR /app

# Copy the application code into the container
COPY . /app

# Update pip
RUN pip install --upgrade pip

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Expose the port your app runs on (Flask default is 5000)
EXPOSE 5105

# Command to run the application
CMD ["python", "app.py"]
