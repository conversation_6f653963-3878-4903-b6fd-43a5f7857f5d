from flask import Flask, request, jsonify
import pandas as pd
import os
import json
from werkzeug.utils import secure_filename
from sqlalchemy import create_engine, text

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'static'

# --- MariaDB connection config ---
DB_USER = 'qcv3user'            # Replace with your MariaDB username
DB_PASSWORD = 'qcv3P@ss'        # Replace with your password
DB_HOST = 'localhost'
DB_PORT = '8308'
DB_NAME = 'qcvent03'

# --- SQLAlchemy engine using pymysql ---
engine = create_engine(f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}")

@app.route

@app.route('/upload_wax', methods=['POST'])
def upload_wax():
    try:
        # 1. Get barcode value from POST
        barcode = request.form.get('barcode')
        if not barcode:
            return jsonify({'error': 'Missing barcode'}), 400

        # 2. Handle file
        if 'waxfile' not in request.files:
            return jsonify({'error': 'No waxfile uploaded'}), 400

        file = request.files['waxfile']
        if file.filename == '':
            return jsonify({'error': 'Empty filename'}), 400

        # Save file to static/ (overwrite if exists)
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        file.save(filepath)

        # 3. Read Excel and normalize columns
        df = pd.read_excel(filepath, engine='openpyxl')
        expected_columns = ['METAL-COLOR', 'NO_THONG', 'NO_PO', 'LINE', 'ITEM', 'SIZE', 'QTT_AWAL', 'PTV']
        df.columns = expected_columns  # Only do this if you're confident the order is always consistent

        # Convert DataFrame to JSON
        json_data = df.to_json(orient='records')

        # 4. Insert barcode + dataframe JSON into MariaDB table `waxinfos`
        with engine.connect() as conn:
            insert_query = text("""
                INSERT INTO waxinfos (barcode, waxdata_json)
                VALUES (:barcode, :waxdata_json)
            """)
            conn.execute(insert_query, {"barcode": barcode, "waxdata_json": json_data})
            conn.commit()

        # 5. Return JSON to frontend
        return jsonify(json.loads(json_data))

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5105)
