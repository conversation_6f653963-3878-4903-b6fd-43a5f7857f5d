from flask import Flask, request, jsonify
from flask_cors import CORS
import pandas as pd
import os
import sys
import json
from werkzeug.utils import secure_filename
from sqlalchemy import create_engine, text
from urllib.parse import quote_plus

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'static'

# Enable CORS for all routes
CORS(app)

# --- MariaDB connection config ---
DB_USER = 'qcv3user'                        # Replace with your MariaDB username
DB_PASSWORD = quote_plus('qcv3P@ss')        # Replace with your password
DB_HOST = 'localhost'
DB_PORT = '8308'
DB_NAME = 'qcvent03'

# --- SQLAlchemy engine using pymysql ---
engine = create_engine(f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}")

@app.route('/',methods=['GET'])
def index_wax():
    data = {'message': 'Hello, World!'}
    return jsonify(data)

@app.route('/upload_wax', methods=['POST'])
def upload_wax():
    try:
        # 1. Get barcode value from POST
        barcode = request.form.get('barcode')
        if not barcode:
            return jsonify({'error': 'Missing barcode'}), 400

        # 2. Handle file
        if 'waxfile' not in request.files:
            return jsonify({'error': 'No waxfile uploaded'}), 400

        file = request.files['waxfile']
        if file.filename == '':
            return jsonify({'error': 'Empty filename'}), 400

        # Save file to static/ (overwrite if exists)
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        file.save(filepath)

        # 3. Read Excel and normalize columns
        df = pd.read_excel(filepath, engine='openpyxl')
        expected_columns = ['METAL-COLOR', 'NO_THONG', 'NO_PO', 'LINE', 'ITEM', 'SIZE', 'QTT_AWAL', 'PTV']
        df.columns = expected_columns  # Only do this if you're confident the order is always consistent

        # Convert DataFrame to JSON
        json_data = df.to_json(orient='records')

        # 4. Insert barcode + dataframe JSON into MariaDB table `waxinfos`
        with engine.connect() as conn:
            insert_query = text("""
                INSERT INTO wax_details (barcode, waxdata_json)
                VALUES (:barcode, :waxdata_json)
            """)
            conn.execute(insert_query, {"barcode": barcode, "waxdata_json": json_data})
            conn.commit()
        # delete uploaded file
        os.remove(filepath)
        
        # 5. Return JSON to frontend
        return jsonify(json.loads(json_data))

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/download_template', methods=['GET'])
def download_template():
    try:
        template_path = os.path.join(app.config['TEMPLATE_FOLDER'], 'wax_template.xlsx')
        return send_file(template_path, as_attachment=True)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Route For Cascutfrm.py
@app.route('/cascutfrm/<barcode>', methods=['GET'])
def cascutfrm(barcode):
    # get barcode from route
    barcode = request.args.get('barcode')

    sys.exit()
    # Get casting order data from database
    with engine.connect() as conn:
        cod =   """
                SELECT * FROM casting_orders WHERE barcode = :barcode AND thong_number IS NOT NULL LIMIT 1
                """
        result = conn.execute(text(cod), {"barcode": barcode})
        casting_order = result.fetchone()

        if casting_order is None:
            return jsonify({'error': 'Casting order not found'}), 404
        
        cols = result.keys()
        casting_order = dict(zip(cols, casting_order))

        # display casting order as json
        return jsonify(casting_order)
        
        # import cascutfrm


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5105)
