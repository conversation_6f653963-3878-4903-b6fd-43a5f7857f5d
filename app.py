# from flask import Flask, request, jsonify
from flask import Flask, send_file, request, render_template_string, url_for, jsonify, redirect
from flask_cors import CORS
import pandas as pd
import os
import sys
import json
from werkzeug.utils import secure_filename
from sqlalchemy import create_engine, text
from urllib.parse import quote_plus
from subprocess import run as shell_run

from cascutfrmb import createform, waxdata
import xlstopdf

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'static'

# Enable CORS for all routes
CORS(app)

# --- MariaDB connection config ---
DB_USER = 'qcv3user'                        # Replace with your MariaDB username
DB_PASSWORD = quote_plus('qcv3P@ss')        # Replace with your password
# DB_HOST = 'localhost'
DB_HOST = 'qcvent03dbm'
DB_PORT = '3306'
DB_NAME = 'qcvent03'

# --- SQLAlchemy engine using pymysql ---
engine1 = create_engine(f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}")

@app.route('/',methods=['GET'])
def index_wax():
    data = {'message': 'Hello, World!'}
    return jsonify(data)

@app.route('/upload_wax', methods=['POST'])
def upload_wax():
    try:
        # 1. Get barcode value from POST
        barcode = request.form.get('barcode')
        if not barcode:
            return jsonify({'error': 'Missing barcode'}), 400

        # 2. Handle file
        if 'waxfile' not in request.files:
            return jsonify({'error': 'No waxfile uploaded'}), 400

        file = request.files['waxfile']
        if file.filename == '':
            return jsonify({'error': 'Empty filename'}), 400

        # Save file to static/ (overwrite if exists)
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        file.save(filepath)

        # 3. Read Excel and normalize columns
        df = pd.read_excel(filepath, engine='openpyxl')
        expected_columns = ['METAL-COLOR', 'NO_THONG', 'NO_PO', 'LINE', 'ITEM', 'SIZE', 'QTT_AWAL', 'PTV']
        df.columns = expected_columns  # Only do this if you're confident the order is always consistent

        # Convert DataFrame to JSON
        json_data = df.to_json(orient='records')

        # 4. Insert barcode + dataframe JSON into MariaDB table `waxinfos`
        with engine1.connect() as conn:
            insert_query = text("""
                INSERT INTO wax_details (barcode, waxdata_json)
                VALUES (:barcode, :waxdata_json)
            """)
            conn.execute(insert_query, {"barcode": barcode, "waxdata_json": json_data})
            conn.commit()
        # delete uploaded file
        os.remove(filepath)
        
        # 5. Return JSON to frontend
        return jsonify(json.loads(json_data))

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/download_template', methods=['GET'])
def download_template():
    try:
        template_path = os.path.join(app.config['TEMPLATE_FOLDER'], 'wax_template.xlsx')
        return send_file(template_path, as_attachment=True)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
@app.route('/waxinfo/<thong_number>', methods=['GET'])
def waxinfo(thong_number):
    waxdetail = waxdata(thong_number)
    return jsonify(waxdetail)

# Route For Cascutfrm.py
@app.route('/cascutfrm/<barcode>', methods=['GET'])
def cascutfrm(barcode):
    # get barcode from @app.route('/cascutfrm/<barcode>'

    thebarcode = barcode
    
    # Get casting order data from database
    with engine1.connect() as conn:
        cod =   """
                SELECT * FROM casting_orders WHERE barcode = :barcode AND thong_number IS NOT NULL LIMIT 1
                """
        result = conn.execute(text(cod), {"barcode": thebarcode})
        casting_order = result.fetchone()    

        if casting_order is None:
            return jsonify({'error': 'Casting order not found'}), 404
        
        createform(casting_order)
        # cols = result.keys()
        # casting_order = dict(zip(cols, casting_order))
        # co = dict(zip(cols, casting_order))
        # ccf=cascutfrm.createform(co['thong_number'],co['barcode'],co['metal'],co['color'],co['order_date'],co['process_date'])
        # print(ccf)
        xlstopdf.convertform('./static/cascutingform.xlsx', './static/cascutingform.pdf')
        # return ("Cek pdf file")
        return redirect(url_for('display_form'))

@app.route("/showccform")
def display_form():
    # return render_template('compyo_pdf.html')
    pdf_url = url_for('static', filename='cascutingform.pdf')
    return render_template_string('''
        <!doctype html>
        <html>
        <head>
            <title>PDF Viewer</title>
        </head>
        <body>
            <h1>View PDF</h1>
            <embed src="{{ pdf_url }}" type="application/pdf" width="100%" height="800px" />
        </body>
        </html>
    ''', pdf_url=pdf_url)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5105)
