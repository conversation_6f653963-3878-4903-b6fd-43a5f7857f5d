Stack trace:
Frame         Function      Args
0007FFFF78F0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF67F0) msys-2.0.dll+0x1FE8E
0007FFFF78F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF7BC8) msys-2.0.dll+0x67F9
0007FFFF78F0  000210046832 (000210286019, 0007FFFF77A8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF78F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF78F0  000210068E24 (0007FFFF7900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF7BD0  00021006A225 (0007FFFF7900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF6F820000 ntdll.dll
7FFF39280000 aswhook.dll
7FFF6D840000 KERNEL32.DLL
7FFF6CDF0000 KERNELBASE.dll
7FFF6D670000 USER32.dll
7FFF6D590000 win32u.dll
7FFF6F650000 GDI32.dll
7FFF6CAA0000 gdi32full.dll
7FFF6D360000 msvcp_win.dll
7FFF6CCA0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF6E9A0000 advapi32.dll
7FFF6DDD0000 msvcrt.dll
7FFF6DAE0000 sechost.dll
7FFF6F6C0000 RPCRT4.dll
7FFF6C090000 CRYPTBASE.DLL
7FFF6CA00000 bcryptPrimitives.dll
7FFF6F680000 IMM32.DLL
