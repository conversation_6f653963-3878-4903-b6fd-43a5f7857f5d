import os
import sys
import subprocess

def convertform(input_path, output_path):
    """
    Convert .xlsx file to .pdf using LibreOffice.
    Falls back to alternative methods if possible.
    """
    input_abs = os.path.abspath(input_path)
    output_dir = os.path.dirname(input_abs)

    # Ensure input file exists
    if not os.path.isfile(input_abs):
        raise FileNotFoundError(f"Input file '{input_abs}' does not exist.")

    try:
        # Use LibreOffice CLI to convert file
        print("Attempting to convert using LibreOffice...")
        subprocess.run([
            'libreoffice', 
            '--headless', 
            '--convert-to', 'pdf', 
            '--outdir', output_dir, 
            input_abs
        ], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        # Output file path
        pdf_file = os.path.splitext(os.path.basename(input_abs))[0] + ".pdf"
        final_output = os.path.join(output_dir, pdf_file)

        # Rename if needed to match desired output path
        if os.path.exists(final_output):
            os.rename(final_output, output_path)
            print(f"Converted file saved as '{output_path}'")
        else:
            raise Exception("PDF file was not created by LibreOffice.")

    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print("LibreOffice not found or conversion failed.")
        try:
            print("Trying with unoconv...")
            import unoconv
            subprocess.run(['unoconv', '-f', 'pdf', '-o', output_path, input_abs], check=True)
            print(f"Converted file saved as '{output_path}'")
        except (ImportError, FileNotFoundError, subprocess.CalledProcessError) as e2:
            print("unoconv also failed.")
            try:
                print("Trying with pyodconverter...")
                from pyodconverter import Converter
                converter = Converter()
                converter.convert(input_abs, output_path)
                print(f"Converted file saved as '{output_path}'")
            except Exception as e3:
                print("All methods failed. Error details:")
                print(f"- LibreOffice error: {e}")
                print(f"- unoconv error: {e2}")
                print(f"- pyodconverter error: {e3}")
                sys.exit(1)