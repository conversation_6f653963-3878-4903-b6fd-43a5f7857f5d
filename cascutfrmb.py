import os
import sys
import json
import barcode
from barcode.writer import ImageWriter
from urllib.parse import quote_plus
from openpyxl import load_workbook
from openpyxl.drawing.image import Image
from openpyxl.drawing.spreadsheet_drawing import An<PERSON><PERSON>ark<PERSON>, OneCellAnchor
from openpyxl.drawing.xdr import XDRPositiveSize2D
from copy import copy
from sqlalchemy import create_engine, text

# Load the workbook and select the active worksheet
wb = load_workbook(os.path.join('template', 'cascutfrmb.xlsx'))
ws = wb.active  # or wb['SheetName'] if you know the sheet name

# Functin to create barcode
def create_barcode(barcode_value):
    barcode_class = barcode.get_barcode_class("code128")
    barcode_obj = barcode_class(barcode_value, writer=ImageWriter())
    # barcode_image_path = "static/barcode.png"
    # store barcode in static folder
    barcode_image_path = os.path.join('static', 'barcode')
    # barcode_name = "barcode"
    # barcode_obj.save(barcode_name)
    barcode_obj.save(barcode_image_path)
    return barcode_image_path

# Function to get casting order data
def orderdata():
    # --- MariaDB connection config ---
    DB_USER = 'invero'                        # Replace with your MariaDB username
    DB_PASSWORD = 'invero'        # Replace with your password
    # DB_PASSWORD = quote_plus('qcv3P@ss')        # Replace with your password
    DB_HOST = '***********'
    DB_PORT = '3306'

# Function to get wax data
def waxdata(thong_number):
    # --- MariaDB connection config ---
    DB_USER = 'invero'                        # Replace with your MariaDB username
    DB_PASSWORD = 'invero'        # Replace with your password
    # DB_PASSWORD = quote_plus('qcv3P@ss')        # Replace with your password
    DB_HOST = '***********'
    DB_PORT = '3306'
    DB_NAME = 'gold2025'

    # --- SQLAlchemy engine using pymysql ---
    engine = create_engine(f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}")

    with engine.connect() as conn:
        select_query = text("""
           SELECT w.thong_number, pod.barcode, pod.no_po, pod.item_number, SUM(pod.qty) AS qty, pod.line_no FROM tb_proses_wax w, tb_proses_order_detail pod WHERE w.thong_number=:thong AND pod.wax_id = w.id GROUP BY pod.barcode, pod.line_no ORDER BY pod.barcode
        """)

        # Execute the query with the thong_number parameter
        result = conn.execute(select_query, {"thong": thong_number})

        # Convert result to list of dictionaries
        columns = result.keys()
        data = []
        for row in result:
            row_dict = dict(zip(columns, row))
            data.append(row_dict)

        return data

def addSimpleImage():
    # Load image
    img = Image('static/barcode.png')

    # Do NOT resize the image – keep original dimensions
    # Simply anchor it to cell A1 (top-left of merged A1:I3)
    ws.add_image(img, 'A1')

def addImage():
    img = Image('static/barcode.png')
    # Set image size to 2.25" x 1"
    # Convert inches to pixels: 1 inch = 96 pixels
    img.width = int(2.25 * 96)   # 216 pixels
    img.height = int(1 * 96)     # 96 pixels

    # Insert image at top-left of merged range A1:I3
    ws.add_image(img, "A1")

# Function to check merged cell in row
def isMerged(row, col):
    for mrange in ws.merged_cells.ranges:
        if (row, col) in mrange:
            return True
    return False

# def createform(data):
def createform(data):
    if data is None:
        return "No data"
    # Create Barcode Image
    # sys.exit()
    create_barcode(data.barcode)
    # Get Wax data
    wax = waxdata(data.thong_number)
    source_start = 13
    # source_end = 13
    row_block_size = 6
    # Put Casting Infomation
    ws['A6'] = data.metal 
    ws['D6'] = data.color
    ws['G6'] = data.thong_number
    ws['K6'] = data.order_date
    ws['N6'] = data.process_date

    
    # Put Barcode Image in A1
    addImage()
    ws['A14'] = wax[0]['no_po']
    ws['A16'] = wax[0]['item_number']
    ws['B17'] = wax[0]['line_no']
    ws['D17'] = wax[0]['qty']

    # List of destination start rows
    repeat = len(wax) - 1
    first_repeat = 19
    destination_starts=[]
    for r in range(repeat):
        destination_starts.append(first_repeat + (r * row_block_size))
    print(destination_starts)

    ii=1
    for dest_start in destination_starts:
        for i in range(row_block_size):
            src_row = source_start + i
            dest_row = dest_start + i
            for col in range(1, ws.max_column + 1):                    
                src_cell = ws.cell(row=src_row, column=col)
                dest_cell = ws.cell(row=dest_row, column=col)
                dest_cell.value = src_cell.value
                dest_cell.font = copy(src_cell.font)
                dest_cell.border = copy(src_cell.border)
                dest_cell.fill = copy(src_cell.fill)
                dest_cell.number_format = copy(src_cell.number_format)
                dest_cell.protection = copy(src_cell.protection)
                dest_cell.alignment = copy(src_cell.alignment)
                
                if i == 1 and col == 1:
                        dest_cell.value = wax[ii]['no_po']
                if i == 3 and col == 1:
                    dest_cell.value = wax[ii]['item_number']
                if i == 4:
                    if col == 2:
                        dest_cell.value = wax[ii]['line_no']
                    if col == 4:
                        dest_cell.value = wax[ii]['qty']
        ii += 1
        # print('Data ke-',ii)

    # Save the modified workbook to static/cascutingform.xlsx
    wb.save(os.path.join('static', 'cascutingform.xlsx'))

    return "Casting Form Created"

def cekobjek(obj):
    return obj