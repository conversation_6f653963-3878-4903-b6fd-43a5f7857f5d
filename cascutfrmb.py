import os
import sys
import json
import barcode
from barcode.writer import ImageWriter
from urllib.parse import quote_plus
from openpyxl import load_workbook
from openpyxl.drawing.image import Image
from openpyxl.drawing.spreadsheet_drawing import An<PERSON>Mark<PERSON>, OneCellAnchor
from openpyxl.drawing.xdr import XDRPositiveSize2D
from copy import copy
from sqlalchemy import create_engine, text
from flask import jsonify

# Load the workbook and select the active worksheet
wb = load_workbook(os.path.join('template', 'cascutfrmb.xlsx'))
ws = wb.active  # or wb['SheetName'] if you know the sheet name

# Functin to create barcode
def create_barcode(barcode_value):
    barcode_class = barcode.get_barcode_class("code128")
    barcode_obj = barcode_class(barcode_value, writer=ImageWriter())
    # barcode_image_path = "static/barcode.png"
    # store barcode in static folder
    barcode_image_path = os.path.join('static', 'barcode')
    # barcode_name = "barcode"
    # barcode_obj.save(barcode_name)
    barcode_obj.save(barcode_image_path)
    return barcode_image_path

# Function to get casting order data
def orderdata():
    # --- MariaDB connection config ---
    DB_USER = 'invero'                        # Replace with your MariaDB username
    DB_PASSWORD = 'invero'        # Replace with your password
    # DB_PASSWORD = quote_plus('qcv3P@ss')        # Replace with your password
    DB_HOST = '***********'
    DB_PORT = '3306'

# Function to get wax data
def waxdata(barcode):
    # --- MariaDB connection config ---
    DB_USER = 'qcv3user'                        # Replace with your MariaDB username
    DB_PASSWORD = quote_plus('qcv3P@ss')        # Replace with your password
    # DB_HOST = 'localhost'
    DB_HOST = 'qcvent03dbm'
    DB_PORT = '3306'
    DB_NAME = 'qcvent03'

    # --- SQLAlchemy engine using pymysql ---
    engine = create_engine(f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}")

    with engine.connect() as conn:
        select_query = text("""
           SELECT * FROM wax_details WHERE barcode = :barcode
        """)

        # Execute the query with the barcode parameter
        result = conn.execute(select_query, {"barcode": barcode})
        # fetch one row and convert to dictionary
        result = result.fetchone()
        result = dict(zip(result.keys(), result))

        return result

def addSimpleImage():
    # Load image
    img = Image('static/barcode.png')

    # Do NOT resize the image – keep original dimensions
    # Simply anchor it to cell A1 (top-left of merged A1:I3)
    ws.add_image(img, 'A1')

def addImage():
    img = Image('static/barcode.png')
    # Set image size to 2.25" x 1"
    # Convert inches to pixels: 1 inch = 96 pixels
    img.width = int(2.25 * 96)   # 216 pixels
    img.height = int(1 * 96)     # 96 pixels

    # Insert image at top-left of merged range A1:I3
    ws.add_image(img, "A1")

# Function to check merged cell in row
def isMerged(row, col):
    for mrange in ws.merged_cells.ranges:
        if (row, col) in mrange:
            return True
    return False

# def createform(data):
def createform(data):
    if data is None:
        return "No data" 
    # Create Barcode Image
    # sys.exit()
    create_barcode(data.barcode)
    # Get Wax data
    wax = waxdata(data.barcode)
    # convert wax.waxdata_json to list of dictionary
    wax = json.loads(wax[0]['waxdata_json'])
    source_start = 13
    # source_end = 13
    row_block_size = 6
    # Put Casting Infomation
    ws['A6'] = data.metal 
    ws['D6'] = data.color
    ws['G6'] = data.thong_number
    ws['K6'] = data.order_date
    ws['N6'] = data.process_date
    ws['Q6'] = data.berat_wax

    
    # Put Barcode Image in A1
    addImage()
    ws['A14'] = wax[0]['no_po']
    ws['A16'] = wax[0]['item_number']
    ws['B17'] = wax[0]['line_no']
    ws['D17'] = wax[0]['qty']

    # List of destination start rows
    # rows = length of wax
    rows = len(wax)
    """
    start_row = 19
    for each row:
        merge column A to D, fill with wax[barcode]
        merge column E to G, fill with wax[no_po]
        merge column H to J, fill with wax[item_number]
        merge column K to L, fill with wax[line_no]
        merge column M to N, fill with wax[qty]
    """
    start_row = 19
    for r in range(rows):
        # merge column A to D, fill with wax[barcode]
        ws.merge_cells(start_row=start_row, start_column=1, end_row=start_row, end_column=4)
        ws.cell(row=start_row, column=1).value = wax[r]['barcode']
        # merge column E to G, fill with wax[no_po]
        ws.merge_cells(start_row=start_row, start_column=5, end_row=start_row, end_column=7)
        ws.cell(row=start_row, column=5).value = wax[r]['no_po']
        # merge column H to J, fill with wax[item_number]
        ws.merge_cells(start_row=start_row, start_column=8, end_row=start_row, end_column=10)
        ws.cell(row=start_row, column=8).value = wax[r]['item_number']
        # merge column K to L, fill with wax[line_no]
        ws.merge_cells(start_row=start_row, start_column=11, end_row=start_row, end_column=12)
        ws.cell(row=start_row, column=11).value = wax[r]['line_no']
        # merge column M to N, fill with wax[qty]
        ws.merge_cells(start_row=start_row, start_column=13, end_row=start_row, end_column=14)
        ws.cell(row=start_row, column=13).value = wax[r]['qty']
        start_row += 1



    # Save the modified workbook to static/cascutingform.xlsx
    wb.save(os.path.join('static', 'cascutingform.xlsx'))

    return "Casting Form Created"

def cekobjek(obj):
    return obj