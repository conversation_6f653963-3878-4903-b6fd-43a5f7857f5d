import os
import sys
import json
import barcode
from barcode.writer import ImageWriter
from urllib.parse import quote_plus
from openpyxl import load_workbook
from openpyxl.drawing.image import Image
from openpyxl.drawing.spreadsheet_drawing import AnchorMark<PERSON>, OneCellAnchor
from openpyxl.drawing.xdr import XDRPositiveSize2D
from openpyxl.styles import Border, Side, Alignment
from copy import copy
from sqlalchemy import create_engine, text
from flask import jsonify

# Load the workbook and select the active worksheet
wb = load_workbook(os.path.join('template', 'cascutfrmc.xlsx'))
ws = wb.active  # or wb['SheetName'] if you know the sheet name

# Functin to create barcode
def create_barcode(barcode_value):
    barcode_class = barcode.get_barcode_class("code128")
    barcode_obj = barcode_class(barcode_value, writer=ImageWriter())
    # barcode_image_path = "static/barcode.png"
    # store barcode in static folder
    barcode_image_path = os.path.join('static', 'barcode')
    # barcode_name = "barcode"
    # barcode_obj.save(barcode_name)
    barcode_obj.save(barcode_image_path)
    return barcode_image_path

# Function to get casting order data
def waxdetail():
    # --- MariaDB connection config ---
    DB_USER = 'qcv3user'                        # Replace with your MariaDB username
    DB_PASSWORD = quote_plus('qcv3P@ss')        # Replace with your password
    DB_HOST = 'qcvent03dbm'
    DB_PORT = '3306'
    DB_NAME = 'qcvent03'

    # --- SQLAlchemy engine using pymysql ---
    engine = create_engine(f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}")

    with engine.connect() as conn:
        select_query = text("""
        SELECT * FROM casting_orders WHERE barcode = :barcode AND thong_number IS NOT NULL LIMIT 1
        """)

        # Execute the query with the barcode parameter
        query_result = conn.execute(select_query, {"barcode": barcode})
        # fetch one row and convert to dictionary
        row = query_result.fetchone()
        if row is None:
            return None
        columns = query_result.keys()
        result = dict(zip(columns, row))
        return result

# Function to get wax data
def waxdata(thong):
    # --- MariaDB connection config ---
    DB_USER = 'invero'                        # Replace with your MariaDB username
    DB_PASSWORD = 'invero'        # Replace with your password
    DB_HOST = '***********'
    # DB_HOST = 'qcvent03dbm'
    DB_PORT = '3306'
    DB_NAME = 'gold2025'

    # --- SQLAlchemy engine using pymysql ---
    engine = create_engine(f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}")

    with engine.connect() as conn:
        select_query = text("""
        SELECT w.thong_number, pod.barcode, pod.no_po, pod.item_number, pod.qty, pod.line_no FROM tb_proses_wax w, tb_proses_order_detail pod WHERE w.thong_number=:thong AND pod.wax_id = w.id AND id_bagian_asal=4 ORDER BY pod.barcode
        """)

        # Execute the query with the barcode parameter
        query_result = conn.execute(select_query, {"thong": thong})
        # fetch one row and convert to dictionary
        # row = query_result.fetchall()

        # Convert row to dictionary
        columns = query_result.keys()
        result = []
        for row in query_result:
            row_dict = dict(zip(columns, row))
            result.append(row_dict)

        return result

def addSimpleImage():
    # Load image
    img = Image('static/barcode.png')

    # Do NOT resize the image – keep original dimensions
    # Simply anchor it to cell A1 (top-left of merged A1:I3)
    ws.add_image(img, 'A1')

def addImage():
    img = Image('static/barcode.png')
    # Set image size to 2.25" x 1"
    # Convert inches to pixels: 1 inch = 96 pixels
    img.width = int(2.25 * 96)   # 216 pixels
    img.height = int(1 * 96)     # 96 pixels

    # Insert image at top-left of merged range A1:I3
    ws.add_image(img, "A1")

# Function to check merged cell in row
def isMerged(row, col):
    for mrange in ws.merged_cells.ranges:
        if (row, col) in mrange:
            return True
    return False

# def createform(data):
def createform(data):
    if data is None:
        return "No data" 
    # Create Barcode Image
    # sys.exit()
    create_barcode(data.barcode)
    # Get Wax data
    wax = waxdetail(data.barcode)
    # Convert wax.waxdata_json to list of dictionary
    wax = json.loads(wax['waxdata_json'])

    # Put Casting Infomation
    ws['A6'] = data.metal 
    ws['D6'] = data.color
    ws['G6'] = data.thong_number
    ws['K6'] = data.order_date
    ws['N6'] = data.process_date
    ws['Q6'] = data.berat_wax

    
    # Put Barcode Image in A1
    addImage()

    # List of destination start rows
    # rows = length of wax
    rows = len(wax)
    """
    start_row = 19
    for each row:
        merge column A to E, fill with wax[barcode]
        merge column F to I, fill with wax[no_po]
        merge column J to L, fill with wax[item_number]
        merge column O to P, no fill
        merge column M to N, no fill
    """
    start_row = 19
    for r in range(rows):
        '''
        Fill column A with wax[barcode]
        Fill column F with wax[no_po]
        Fill column J with wax[item_number]
        Fill column M with wax[line_no]
        Fill column N with wax[qty]
        '''
        # Column Used = AFJMNOQ
        # First, merge columns A to E for barcode
        merge1 = f"A{start_row}:E{start_row}"
        ws.merge_cells(merge1)
        # Merge columns F to I for no_po
        merge2 = f"F{start_row}:I{start_row}"
        ws.merge_cells(merge2)
        # Merge columns J to L for item_number
        merge3 = f"J{start_row}:L{start_row}"
        ws.merge_cells(merge3)
        # Merge columns K to L for line_no
        merge4 = f"K{start_row}:L{start_row}"
        ws.merge_cells(merge4)
        # Merge columns O to P for qty
        merge5 = f"O{start_row}:P{start_row}"
        ws.merge_cells(merge5)
        # Merge column Q to R for blank
        merge6 = f"Q{start_row}:R{start_row}"
        ws.merge_cells(merge6)



        # Create black border style
        black_border = Border(
            left=Side(style='thin', color='000000'),
            right=Side(style='thin', color='000000'),
            top=Side(style='thin', color='000000'),
            bottom=Side(style='thin', color='000000')
        )

        for col in range(1, ws.max_column + 1):
            # if isMerged(start_row, col):
            #     continue
            cell = ws.cell(row=start_row, column=col)
            # start_row height = 15
            ws.row_dimensions[start_row].height = 15
            if col == 1:
                cell.value = wax[r]['barcode']
                # Get original font from template (you can reference a template cell)
                template_cell = ws.cell(row=20, column=1)  # Assuming row 19 is your template
                cell.font = copy(template_cell.font)
                # Apply black border to merged range A:E
                for merge_col in range(1, 6):  # Columns A to E
                    merge_cell = ws.cell(row=start_row, column=merge_col)
                    merge_cell.border = black_border
                    merge_cell.alignment = Alignment(horizontal='left',indent=1.0)
            if col == 6:
                cell.value=wax[r]['no_po']
                # Get original font from template (you can reference a template cell)
                template_cell = ws.cell(row=20, column=6)  # Assuming row 19 is your template
                cell.font = copy(template_cell.font)
                # Apply black border to merged range F:I
                for merge_col in range(6, 10):  # Columns F to I
                    merge_cell = ws.cell(row=start_row, column=merge_col)
                    merge_cell.border = black_border
                    merge_cell.alignment = Alignment(horizontal='left',indent=1.0)
            if col == 10:
                cell.value = wax[r]['item_number']
                # Get original font from template (you can reference a template cell)
                template_cell = ws.cell(row=20, column=10)  # Assuming row 19 is your template
                cell.font = copy(template_cell.font)
                # Apply black border to merged range J:L
                for merge_col in range(10, 13):  # Columns J to L
                    merge_cell = ws.cell(row=start_row, column=merge_col)
                    merge_cell.border = black_border
                    merge_cell.alignment = Alignment(horizontal='left',indent=1.0)
            if col == 13:
                cell.value = wax[r]['line_no']
                # Get original font from template (you can reference a template cell)
                template_cell = ws.cell(row=20, column=13)  # Assuming row 19 is your template
                cell.font = copy(template_cell.font)
                # center align
                cell.alignment = Alignment(horizontal='center')
                # Apply black border to merged range M
                for merge_col in range(13, 14):  # Columns K
                    merge_cell = ws.cell(row=start_row, column=merge_col)
                    merge_cell.border = black_border
            if col == 14:
                cell.value = wax[r]['qty']
                # Get original font from template (you can reference a template cell)
                template_cell = ws.cell(row=20, column=14)  # Assuming row 19 is your template
                cell.font = copy(template_cell.font)
                # align center
                cell.alignment = Alignment(horizontal='center')
                # Apply black border to merged range N
                for merge_col in range(14, 15):  # Columns N
                    merge_cell = ws.cell(row=start_row, column=merge_col)
                    merge_cell.border = black_border
            if col == 15:
                # cell.value = ""
                # Get original font from template (you can reference a template cell)
                template_cell = ws.cell(row=20, column=15)  # Assuming row 19 is your template
                cell.font = copy(template_cell.font)
                # Apply black border to merged range O:P
                for merge_col in range(15, 17):  # Columns O to P
                    merge_cell = ws.cell(row=start_row, column=merge_col)   # Assuming row 19 is your template
                    merge_cell.border = black_border
            if col == 17:
                # cell.value = ""
                # Get original font from template (you can reference a template cell)
                template_cell = ws.cell(row=20, column=17)  # Assuming row 19 is your template
                cell.font = copy(template_cell.font)
                # Apply black border to merged range Q:R
                for merge_col in range(17, 19):  # Columns Q to R
                    merge_cell = ws.cell(row=start_row, column=merge_col)
                    merge_cell.border = black_border
                                                  
        start_row += 1



    # Save the modified workbook to static/cascutingform.xlsx
    wb.save(os.path.join('static', 'cascutingform.xlsx'))

    return "Casting Form Created"

def cekobjek(obj):
    return obj

# url to test: http://localhost:5105/cascutfrm/9K-WG-027-250719